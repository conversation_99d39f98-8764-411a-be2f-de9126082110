{"files.eol": "\n", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.fixAll.stylelint": "always"}, "css.validate": false, "css.format.enable": true, "scss.validate": false, "scss.format.enable": true, "html.format.enable": true, "json.format.enable": true, "eslint.format.enable": true, "javascript.format.enable": true, "typescript.format.enable": true, "typescript.preferences.importModuleSpecifier": "non-relative", "stylelint.validate": ["css", "scss"], "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}