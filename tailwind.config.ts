import { heroui } from '@heroui/react'
import defaultTheme from 'tailwindcss/defaultTheme'
import TailwindOpentypePlugin from 'tailwindcss-opentype'
import TailwindRtlPlugin from 'tailwindcss-rtl'
import type { Config } from 'tailwindcss'

const extSpacing = {
  ...defaultTheme.spacing,
  '1/10': '10%',
  '2/10': '20%',
  '3/10': '30%',
  '4/10': '40%',
  '5/10': '50%',
  '6/10': '60%',
  '7/10': '70%',
  '8/10': '80%',
  '9/10': '90%',
  double: '200%',
  inherit: 'inherit',
  4.5: '1.125rem',
  5.5: '1.375rem',
  6.5: '1.625rem',
  7.5: '1.875rem',
  15: '3.75rem',
  16: '4rem',
  17: '4.25rem',
  18: '4.5rem',
  19: '4.75rem',
  20: '5rem',
  21: '5.25rem',
  22: '5.5rem'
}

const config: Config = {
  darkMode: ['class', "[class~='dark']"],
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx,scss,sass,svg}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    fontFamily: {
      mulish: ['Mulish', ...defaultTheme.fontFamily.sans]
    },
    extend: {
      padding: { ...extSpacing },
      margin: { ...extSpacing },
      width: { ...extSpacing },
      height: { ...extSpacing },
      minWidth: { ...extSpacing },
      maxWidth: { ...extSpacing },
      minHeight: { ...extSpacing },
      maxHeight: { ...extSpacing },
      spacing: { ...extSpacing },
      screens: {
        xxs: '420px',
        xs: '540px'
      },
      zIndex: {
        60: '60',
        70: '70',
        80: '80',
        90: '90',
        100: '100',
        full: '999999'
      },
      content: {
        empty: '""'
      }
    }
  },
  plugins: [
    TailwindRtlPlugin,
    TailwindOpentypePlugin,
    heroui({
      layout: {},
      themes: {
        light: {
          layout: {},
          colors: {
            background: '#FFFFFF',
            foreground: '#1C252E',
            focus: '#05B4C7',
            primary: {
              50: '#E6F8F9',
              100: '#B4E9EE',
              200: '#82DAE3',
              300: '#69D2DD',
              400: '#37C3D2',
              500: '#05B4C7',
              600: '#047E8B',
              700: '#035A64',
              800: '#01363C',
              900: '#001214',
              DEFAULT: '#05B4C7',
              foreground: '#FFFFFF'
            }
          }
        },
        dark: {
          layout: {},
          colors: {
            background: '#141A21',
            foreground: '#FFFFFF',
            focus: '#05B4C7',
            primary: {
              50: '#E6F8F9',
              100: '#B4E9EE',
              200: '#82DAE3',
              300: '#69D2DD',
              400: '#37C3D2',
              500: '#05B4C7',
              600: '#047E8B',
              700: '#035A64',
              800: '#01363C',
              900: '#001214',
              DEFAULT: '#05B4C7',
              foreground: '#FFFFFF'
            }
          }
        }
      }
    })
  ]
}

export default config
