import { getHashDigest } from 'loader-utils'
import createNextIntlPlugin from 'next-intl/plugin'
import withNextVides from 'next-videos'
import StylelintPlugin from 'stylelint-webpack-plugin'

/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: process.env.NODE_ENV === 'production',
  poweredByHeader: false,
  experimental: {
    slowModuleDetection: { buildTimeThresholdMs: 1000 },
    optimizePackageImports: [
      '@heroui/react',
      '@reduxjs/toolkit',
      'axios',
      'framer-motion',
      'next-intl',
      'react-hook-form',
      'usehooks-ts',
      'yup'
    ]
  },
  webpack: (config) => {
    const rules = config.module.rules
      .find((rule) => typeof rule.oneOf === 'object')
      .oneOf.filter((rule) => Array.isArray(rule.use))

    process.env.NODE_ENV === 'production' &&
      rules.forEach((rule) => {
        rule.use.forEach((moduleLoader) => {
          if (
            moduleLoader.loader?.includes('css-loader') &&
            !moduleLoader.loader?.includes('postcss-loader') &&
            !moduleLoader.options?.fontLoader
          ) {
            const cssModule = moduleLoader.options.modules

            if (cssModule) {
              const { getLocalIdent } = cssModule

              cssModule.getLocalIdent = (context, _, exportName, options) =>
                getHashDigest(
                  Buffer.from(
                    `${context.resourcePath}:${getLocalIdent(context, _, exportName, options)}`
                  ),
                  'sha1',
                  'base64',
                  32
                )
                  .replace(/[^a-zA-Z0-9-_]/g, '_')
                  .replace(/^(-?\d|--)/, '_$1')
            }
          }
        })
      })

    config.plugins.push(
      new StylelintPlugin({ extensions: ['css', 'scss', 'sass'] })
    )

    config.resolve.extensions.push(
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.css',
      '.scss',
      '.png',
      '.jpg',
      '.jpeg',
      '.svg',
      '.ico',
      '.gif',
      '.woff',
      '.woff2',
      '.eot',
      '.ttf',
      '.otf'
    )

    const svgLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg')
    )

    config.module.rules.push(
      {
        ...svgLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/
      },
      {
        test: /\.svg$/i,
        resourceQuery: { not: [...svgLoaderRule.resourceQuery.not, /url/] },
        issuer: svgLoaderRule.issuer,
        use: ['@svgr/webpack']
      }
    )

    return config
  },
  rewrites: () => {
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL
    const apiOrigin = process.env.NEXT_PUBLIC_API_ORIGIN_URL

    return [
      {
        source: `${apiBase}/login`,
        destination: `${apiOrigin}/j_spring_security_check`
      },
      {
        source: `${apiBase}/:path*`,
        destination: `${apiOrigin}/:path*`
      }
    ]
  }
}

export default createNextIntlPlugin()(withNextVides(nextConfig))
