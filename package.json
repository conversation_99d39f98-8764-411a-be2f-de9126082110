{"name": "nextjs-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint && stylelint \"**/*.{css,scss}\"", "lint:fix": "next lint --fix && stylelint \"**/*.{css,scss}\" --fix", "golikehell": "next build && next start"}, "dependencies": {"@bprogress/next": "3.2.12", "@heroui/react": "2.7.10", "@hookform/resolvers": "5.1.1", "@reduxjs/toolkit": "2.8.2", "axios": "1.9.0", "framer-motion": "12.14.0", "next": "15.3.2", "next-intl": "4.1.0", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-google-recaptcha": "3.1.0", "react-hook-form": "7.57.0", "react-redux": "9.2.0", "usehooks-ts": "3.1.1", "yup": "1.6.1"}, "devDependencies": {"@eslint/compat": "1.2.9", "@eslint/eslintrc": "3.3.1", "@stylistic/eslint-plugin": "4.4.0", "@svgr/webpack": "8.1.0", "@total-typescript/ts-reset": "0.6.1", "@types/node": "22.15.21", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-google-recaptcha": "2.1.9", "@types/stylelint": "14.0.0", "autoprefixer": "10.4.21", "clsx": "2.1.1", "dot-prop": "9.0.0", "eslint": "9.27.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-unused-imports": "4.1.4", "next-videos": "1.4.1", "postcss": "8.5.3", "postcss-import": "16.1.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "sass": "1.89.0", "stylelint": "16.19.1", "stylelint-config-standard-scss": "15.0.1", "stylelint-prettier": "5.0.3", "stylelint-webpack-plugin": "5.0.1", "tailwindcss": "3.4.17", "tailwindcss-opentype": "1.1.0", "tailwindcss-rtl": "0.9.0", "typescript": "5.8.3", "typescript-eslint": "8.32.1"}}