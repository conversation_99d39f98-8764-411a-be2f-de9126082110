import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { fixupConfigRules, fixupPluginRules } from '@eslint/compat'
import { FlatCompat } from '@eslint/eslintrc'
import eslintJs from '@eslint/js'
import stylisticPlugin from '@stylistic/eslint-plugin'
import prettierPlugin from 'eslint-plugin-prettier'
import unusedImportsPlugin from 'eslint-plugin-unused-imports'
import eslintTs from 'typescript-eslint'

const config = [
  ...fixupConfigRules(
    new FlatCompat({
      baseDirectory: dirname(fileURLToPath(import.meta.url)),
      recommendedConfig: eslintJs.configs.recommended
    }).extends(
      'eslint:recommended',
      'next/core-web-vitals',
      'plugin:prettier/recommended'
    )
  ),
  {
    plugins: {
      '@stylistic': fixupPluginRules(stylisticPlugin),
      'unused-imports': fixupPluginRules(unusedImportsPlugin),
      prettier: fixupPluginRules(prettierPlugin)
    },
    rules: {
      // --- Possible Problems ----------------------------------
      'array-callback-return': 'error',
      'no-await-in-loop': 'error',
      'no-constant-condition': ['error', { checkLoops: 'all' }],
      'no-duplicate-imports': ['error', { includeExports: true }],
      'no-fallthrough': [
        'error',
        { allowEmptyCase: true, reportUnusedFallthroughComment: true }
      ],
      'no-inner-declarations': 'error',
      'no-promise-executor-return': 'error',
      'no-self-compare': 'error',
      'no-template-curly-in-string': 'error',
      'no-unassigned-vars': 'error',
      'no-unmodified-loop-condition': 'error',
      'no-unreachable-loop': 'error',
      'no-useless-assignment': 'error',
      // --- Suggestions ----------------------------------------
      'arrow-body-style': 'error',
      'block-scoped-var': 'error',
      camelcase: ['error', { properties: 'never' }],
      'consistent-return': 'error',
      curly: ['error', 'multi'],
      'default-case': 'error',
      'default-case-last': 'error',
      'default-param-last': 'error',
      'dot-notation': 'error',
      eqeqeq: 'error',
      'func-style': 'error',
      'id-length': [
        'error',
        { min: 3, properties: 'never', exceptions: ['_', 'fs', 't'] }
      ],
      'id-match': [
        'error',
        '^[A-Za-z](?:[A-Za-z0-9_]*[A-Za-z0-9])?$',
        { onlyDeclarations: true }
      ],
      'init-declarations': 'error',
      'logical-assignment-operators': 'error',
      'new-cap': 'error',
      'no-alert': 'error',
      'no-array-constructor': 'error',
      'no-bitwise': 'error',
      'no-caller': 'error',
      'no-console': 'error',
      'no-continue': 'error',
      'no-div-regex': 'error',
      'no-else-return': ['error', { allowElseIf: false }],
      'no-empty-function': 'error',
      'no-eq-null': 'error',
      'no-eval': 'error',
      'no-extra-bind': 'error',
      'no-extra-label': 'error',
      'no-implied-eval': 'error',
      'no-inline-comments': 'error',
      'no-label-var': 'error',
      'no-labels': 'error',
      'no-lone-blocks': 'error',
      'no-lonely-if': 'error',
      'no-loop-func': 'error',
      'no-multi-assign': 'error',
      'no-multi-str': 'error',
      'no-negated-condition': 'error',
      'no-new': 'error',
      'no-new-func': 'error',
      'no-new-wrappers': 'error',
      'no-object-constructor': 'error',
      'no-octal-escape': 'error',
      'no-param-reassign': 'error',
      'no-return-assign': 'error',
      'no-script-url': 'error',
      'no-sequences': 'error',
      'no-throw-literal': 'error',
      'no-undef-init': 'error',
      'no-unneeded-ternary': 'error',
      'no-unused-expressions': [
        'error',
        { allowShortCircuit: true, allowTernary: true }
      ],
      'no-useless-computed-key': 'error',
      'no-useless-concat': 'error',
      'no-useless-rename': 'error',
      'no-useless-return': 'error',
      'no-var': 'error',
      'no-void': 'error',
      'object-shorthand': 'error',
      'one-var': ['error', 'never'],
      'operator-assignment': 'error',
      'prefer-arrow-callback': 'error',
      'prefer-const': 'error',
      'prefer-destructuring': [
        'error',
        { array: false, object: true },
        { enforceForRenamedProperties: false }
      ],
      'prefer-exponentiation-operator': 'error',
      'prefer-object-spread': 'error',
      'prefer-promise-reject-errors': ['error', { allowEmptyReject: true }],
      'prefer-regex-literals': ['error', { disallowRedundantWrapping: true }],
      'prefer-template': 'error',
      'require-await': 'error',
      'vars-on-top': 'error',
      yoda: 'error',
      // --- Layout & Formatting --------------------------------
      'unicode-bom': 'error',
      // --- Stylistic ------------------------------------------
      '@stylistic/array-bracket-newline': ['error', 'consistent'],
      '@stylistic/array-bracket-spacing': 'error',
      '@stylistic/array-element-newline': ['error', 'consistent'],
      '@stylistic/arrow-parens': 'error',
      '@stylistic/arrow-spacing': 'error',
      '@stylistic/block-spacing': 'error',
      '@stylistic/brace-style': ['error', '1tbs', { allowSingleLine: true }],
      '@stylistic/comma-dangle': 'error',
      '@stylistic/comma-spacing': 'error',
      '@stylistic/comma-style': 'error',
      '@stylistic/computed-property-spacing': 'error',
      '@stylistic/curly-newline': ['error', { consistent: true }],
      '@stylistic/dot-location': ['error', 'property'],
      '@stylistic/eol-last': 'error',
      '@stylistic/function-call-argument-newline': ['error', 'consistent'],
      '@stylistic/function-call-spacing': 'error',
      '@stylistic/generator-star-spacing': ['error', 'both'],
      '@stylistic/jsx-closing-bracket-location': [
        'error',
        { selfClosing: 'line-aligned', nonEmpty: 'after-props' }
      ],
      '@stylistic/jsx-closing-tag-location': ['error', 'line-aligned'],
      '@stylistic/jsx-curly-brace-presence': [
        'error',
        { children: 'never', props: 'never', propElementValues: 'always' }
      ],
      '@stylistic/jsx-curly-spacing': 'error',
      '@stylistic/jsx-equals-spacing': 'error',
      '@stylistic/jsx-first-prop-new-line': ['error', 'multiline'],
      '@stylistic/jsx-function-call-newline': ['error', 'multiline'],
      '@stylistic/jsx-indent-props': ['error', 2],
      '@stylistic/jsx-newline': ['error', { prevent: true }],
      '@stylistic/jsx-pascal-case': 'error',
      '@stylistic/jsx-props-no-multi-spaces': 'error',
      '@stylistic/jsx-quotes': ['error', 'prefer-single'],
      '@stylistic/jsx-self-closing-comp': [
        'error',
        { component: true, html: true }
      ],
      '@stylistic/jsx-sort-props': [
        'error',
        {
          ignoreCase: true,
          callbacksLast: true,
          shorthandFirst: true,
          shorthandLast: false,
          noSortAlphabetically: true,
          reservedFirst: true
        }
      ],
      '@stylistic/jsx-tag-spacing': [
        'error',
        {
          closingSlash: 'never',
          beforeSelfClosing: 'always',
          afterOpening: 'never',
          beforeClosing: 'never'
        }
      ],
      '@stylistic/key-spacing': 'error',
      '@stylistic/keyword-spacing': 'error',
      '@stylistic/line-comment-position': 'error',
      '@stylistic/linebreak-style': 'error',
      '@stylistic/lines-around-comment': [
        'error',
        {
          beforeBlockComment: false,
          beforeLineComment: false,
          allowBlockStart: true,
          allowClassStart: true,
          allowObjectStart: true,
          allowArrayStart: true
        }
      ],
      '@stylistic/lines-between-class-members': 'error',
      '@stylistic/max-len': [
        'error',
        {
          code: 80,
          tabWidth: 2,
          ignoreUrls: true,
          ignoreStrings: true,
          ignoreComments: true,
          ignoreRegExpLiterals: true,
          ignoreTemplateLiterals: true,
          ignorePattern: '^\\s*import\\s.+\\s+from\\s.+\\s*;?$'
        }
      ],
      '@stylistic/max-statements-per-line': 'error',
      '@stylistic/member-delimiter-style': [
        'error',
        {
          multiline: { delimiter: 'none' },
          singleline: { delimiter: 'semi' }
        }
      ],
      '@stylistic/multiline-comment-style': ['error', 'separate-lines'],
      '@stylistic/new-parens': 'error',
      '@stylistic/no-confusing-arrow': ['error', { onlyOneSimpleParam: true }],
      '@stylistic/no-extra-semi': 'error',
      '@stylistic/no-floating-decimal': 'error',
      '@stylistic/no-mixed-operators': 'error',
      '@stylistic/no-mixed-spaces-and-tabs': 'error',
      '@stylistic/no-multi-spaces': 'error',
      '@stylistic/no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }],
      '@stylistic/no-tabs': 'error',
      '@stylistic/no-trailing-spaces': 'error',
      '@stylistic/no-whitespace-before-property': 'error',
      '@stylistic/nonblock-statement-body-position': ['error', 'any'],
      '@stylistic/object-curly-newline': 'error',
      '@stylistic/object-curly-spacing': ['error', 'always'],
      '@stylistic/object-property-newline': [
        'error',
        { allowAllPropertiesOnSameLine: true }
      ],
      '@stylistic/one-var-declaration-per-line': 'error',
      '@stylistic/operator-linebreak': [
        'error',
        'after',
        { overrides: { '?': 'ignore', ':': 'ignore' } }
      ],
      '@stylistic/padded-blocks': ['error', 'never'],
      '@stylistic/padding-line-between-statements': [
        'error',
        { blankLine: 'always', prev: '*', next: '*' },
        {
          blankLine: 'never',
          prev: '*',
          next: ['enum', 'interface', 'type']
        },
        {
          blankLine: 'never',
          prev: ['enum', 'interface', 'type'],
          next: '*'
        },
        {
          blankLine: 'always',
          prev: ['enum', 'interface', 'type'],
          next: ['export']
        },
        {
          blankLine: 'always',
          prev: [
            'const',
            'let',
            'var',
            'multiline-const',
            'multiline-let',
            'multiline-var'
          ],
          next: ['enum', 'interface', 'type']
        },
        {
          blankLine: 'never',
          prev: ['directive'],
          next: ['directive']
        },
        {
          blankLine: 'never',
          prev: ['*'],
          next: ['block-like']
        },
        {
          blankLine: 'never',
          prev: ['block-like'],
          next: ['*']
        },
        {
          blankLine: 'always',
          prev: ['multiline-block-like'],
          next: ['*']
        },
        {
          blankLine: 'always',
          prev: ['*'],
          next: ['multiline-block-like']
        },
        {
          blankLine: 'never',
          prev: ['expression'],
          next: ['expression']
        },
        {
          blankLine: 'always',
          prev: ['multiline-expression'],
          next: ['*']
        },
        {
          blankLine: 'always',
          prev: ['*'],
          next: ['multiline-expression']
        },
        {
          blankLine: 'never',
          prev: ['const', 'let', 'var'],
          next: ['const', 'let', 'var']
        },
        {
          blankLine: 'always',
          prev: ['multiline-const', 'multiline-let', 'multiline-var'],
          next: ['*']
        },
        {
          blankLine: 'always',
          prev: ['*'],
          next: ['multiline-const', 'multiline-let', 'multiline-var']
        },
        {
          blankLine: 'always',
          prev: ['export'],
          next: ['export']
        },
        {
          blankLine: 'always',
          prev: ['*'],
          next: ['import']
        },
        {
          blankLine: 'always',
          prev: ['import'],
          next: ['*']
        },
        {
          blankLine: 'never',
          prev: ['import'],
          next: ['import']
        }
      ],
      '@stylistic/quote-props': ['error', 'as-needed'],
      '@stylistic/quotes': [
        'error',
        'single',
        { avoidEscape: true, allowTemplateLiterals: true }
      ],
      '@stylistic/rest-spread-spacing': 'error',
      '@stylistic/semi': ['error', 'never'],
      '@stylistic/semi-spacing': 'error',
      '@stylistic/semi-style': 'error',
      '@stylistic/space-before-blocks': 'error',
      '@stylistic/space-before-function-paren': [
        'error',
        { anonymous: 'never', named: 'never', asyncArrow: 'always' }
      ],
      '@stylistic/space-in-parens': 'error',
      '@stylistic/space-infix-ops': 'error',
      '@stylistic/space-unary-ops': 'error',
      '@stylistic/switch-colon-spacing': 'error',
      '@stylistic/template-curly-spacing': 'error',
      '@stylistic/template-tag-spacing': 'error',
      '@stylistic/type-annotation-spacing': [
        'error',
        {
          before: true,
          after: true,
          overrides: { colon: { before: false, after: true } }
        }
      ],
      '@stylistic/type-generic-spacing': 'error',
      '@stylistic/type-named-tuple-spacing': 'error',
      '@stylistic/wrap-iife': 'error',
      '@stylistic/yield-star-spacing': ['error', 'both'],
      // --- Misc -----------------------------------------------
      'unused-imports/no-unused-imports': 'error',
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            'internal',
            'parent',
            'sibling',
            'index',
            'object',
            'type',
            'unknown'
          ],
          pathGroups: [
            {
              pattern: 'react',
              group: 'external',
              position: 'before'
            },
            {
              pattern: 'react/**',
              group: 'external',
              position: 'before'
            },
            {
              pattern: 'next',
              group: 'external',
              position: 'before'
            },
            {
              pattern: 'next/**',
              group: 'external',
              position: 'before'
            },
            {
              pattern: '@/**',
              group: 'internal',
              position: 'after'
            },
            {
              pattern: '@styles/**',
              group: 'unknown',
              position: 'after'
            },
            {
              pattern: '**/*.module',
              group: 'type',
              position: 'after'
            },
            {
              pattern: '**/*.module.*',
              group: 'type',
              position: 'after'
            }
          ],
          pathGroupsExcludedImportTypes: ['react', 'next'],
          named: {
            enabled: true,
            import: true,
            export: true,
            require: true,
            cjsExports: true,
            types: 'types-first'
          },
          alphabetize: {
            order: 'asc',
            caseInsensitive: true
          },
          warnOnUnassignedImports: true
        }
      ],
      'react-hooks/exhaustive-deps': 'off'
    }
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.mts', '**/*.cts'],
    languageOptions: {
      parser: eslintTs.parser,
      parserOptions: {
        project: true
      }
    },
    plugins: {
      '@typescript-eslint': fixupPluginRules(eslintTs.plugin)
    },
    rules: {
      ...[
        ...eslintTs.configs.recommended,
        ...eslintTs.configs.stylisticTypeChecked
      ].reduce((acc, { rules }) => Object.assign(acc, rules || {}), {}),
      '@typescript-eslint/no-explicit-any': [
        'error',
        {
          fixToUnknown: true,
          ignoreRestArgs: true
        }
      ],
      // --- Recommended Type Check -----------------------------
      '@typescript-eslint/await-thenable': 'error',
      '@typescript-eslint/no-duplicate-type-constituents': 'error',
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/no-for-in-array': 'error',
      '@typescript-eslint/no-redundant-type-constituents': 'error',
      '@typescript-eslint/no-unnecessary-type-assertion': 'error',
      '@typescript-eslint/no-unsafe-enum-comparison': 'error',
      '@typescript-eslint/no-unsafe-unary-minus': 'error',
      '@typescript-eslint/restrict-plus-operands': 'error',
      '@typescript-eslint/restrict-template-expressions': 'error',
      // --- Basic and Strict -----------------------------------
      '@typescript-eslint/consistent-type-exports': 'error',
      '@typescript-eslint/consistent-type-imports': [
        'error',
        {
          fixStyle: 'inline-type-imports'
        }
      ],
      '@typescript-eslint/method-signature-style': 'error',
      '@typescript-eslint/no-deprecated': 'error',
      '@typescript-eslint/no-meaningless-void-operator': 'error',
      '@typescript-eslint/no-mixed-enums': 'error',
      '@typescript-eslint/no-non-null-asserted-nullish-coalescing': 'error',
      '@typescript-eslint/no-unnecessary-boolean-literal-compare': 'error',
      '@typescript-eslint/no-unnecessary-condition': 'error',
      '@typescript-eslint/no-unnecessary-qualifier': 'error',
      '@typescript-eslint/no-unnecessary-template-expression': 'error',
      '@typescript-eslint/no-unnecessary-type-arguments': 'error',
      '@typescript-eslint/no-unnecessary-type-conversion': 'error',
      '@typescript-eslint/no-unnecessary-type-parameters': 'error',
      '@typescript-eslint/no-useless-empty-export': 'error',
      '@typescript-eslint/prefer-enum-initializers': 'error',
      '@typescript-eslint/prefer-literal-enum-member': 'error',
      '@typescript-eslint/prefer-reduce-type-parameter': 'error',
      '@typescript-eslint/require-array-sort-compare': 'error',
      '@typescript-eslint/unified-signatures': 'error'
    }
  }
]

export default config
