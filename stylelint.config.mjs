/** @type {import('stylelint').Config} */

const config = {
  extends: ['stylelint-config-standard-scss'],
  plugins: ['stylelint-prettier'],
  rules: {
    // --- Deprecated -----------------------------------------
    'at-rule-no-deprecated': [
      true,
      {
        ignoreAtRules: [
          'use',
          'tailwind',
          'layer',
          'variants',
          'responsive',
          'screen',
          'apply'
        ]
      }
    ],
    // --- Invalid --------------------------------------------
    'color-no-invalid-hex': true,
    // --- Non-Standard ---------------------------------------
    'function-linear-gradient-no-nonstandard-direction': true,
    // --- Unknown --------------------------------------------
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'use',
          'tailwind',
          'layer',
          'variants',
          'responsive',
          'screen',
          'apply'
        ]
      }
    ],
    'scss/at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'use',
          'tailwind',
          'layer',
          'variants',
          'responsive',
          'screen',
          'apply'
        ]
      }
    ],
    'function-no-unknown': true,
    'no-unknown-animations': true,
    'no-unknown-custom-media': true,
    'no-unknown-custom-properties': true,
    'unit-no-unknown': true,
    // --- Enforce Conventions --------------------------------
    'at-rule-property-required-list': {
      'font-face': [
        'font-display',
        'font-family',
        'font-style',
        'font-weight',
        'src'
      ]
    },
    'function-url-no-scheme-relative': true,
    'at-rule-empty-line-before': [
      'always',
      {
        except: 'inside-block',
        ignoreAtRules: ['use', 'tailwind']
      }
    ],
    'comment-empty-line-before': 'never',
    'custom-property-empty-line-before': 'never',
    'declaration-empty-line-before': 'never',
    'selector-max-universal': 2,
    'selector-class-pattern': /^-?([a-z][a-z0-9]*)((-|__)[a-z0-9]+)*$/,
    // --- Prettier -------------------------------------------
    'prettier/prettier': true
  }
}

export default config
