import { getRequestConfig } from 'next-intl/server'
import {
  getLanguage,
  getTranslationMessages,
  providerMessageFallback,
  providerOnError
} from '@i18n/services'

export default getRequestConfig(async () => {
  const locale = await getLanguage()
  const messages = await getTranslationMessages()

  return {
    locale,
    messages,
    getMessageFallback: providerMessageFallback,
    onError: providerOnError
  }
})
