import { type IntlConfig } from 'next-intl'
import { type LanguageSlug, defaultSlug } from '@i18n/settings'
import { getCookieValue, setCookieItem } from '@storage/cookie'
import { APP_CURRENT_LANGUAGE_SLUG } from '@storage/keys'

export const getTranslationMessages = async () => {
  const locale = await getLanguage()

  return (await import(`./locales/${locale}.json`)).default
}

export const getLanguage = async () =>
  (await getCookieValue<LanguageSlug>(APP_CURRENT_LANGUAGE_SLUG)) ?? defaultSlug

export const setLanguage = async (slug: LanguageSlug) =>
  await setCookieItem(APP_CURRENT_LANGUAGE_SLUG, slug)

export const providerMessageFallback: IntlConfig['getMessageFallback'] = ({
  namespace,
  key
}) => [namespace, key].filter((part) => !!part).join('.')

export const providerOnError: IntlConfig['onError'] = () => null
