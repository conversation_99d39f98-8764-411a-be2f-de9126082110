{"common": {"languages": {"tr": "Türkçe", "en": "English"}, "colorMode": {"light": "Light", "dark": "Dark", "system": "System Theme"}, "route": {"passwordChange": "Change Password", "logout": "Logout"}, "validation": {"usernameReq": "Please enter your username.", "usernameMin": "Your username must be at least {len} characters.", "usernameMax": "Your username must be at most {len} characters.", "passwordReq": "Please enter your password.", "passwordMin": "Your password must be at least {len} characters.", "passwordMax": "Your password must be at most {len} characters."}, "error": {"UNHANDLED_ERROR": "An unhandled error occurred.", "ERR_BAD_REQUEST": "An error occurred while processing your request.", "asyncJobs": "An error occurred while fetching data. Please try again later.", "captchaConfigCantFetched": "Captcha configuration could not be fetched!", "userConfigCantFetched": "User information could not be fetched!", "loginUserNotFound": "Username or password is incorrect."}, "tryAgainAction": "Try Again"}, "app": {"meta": {"title": {"template": "%s | TT Mobility Api Gateway UI", "default": "TT Mobility Api Gateway UI"}}}, "homepage": {"title": "Demo Home Page"}, "loginPage": {"pageTitle": "<PERSON><PERSON>", "form": {"header": {"mainTitle": "Mobility Api Gateway UI", "title": "Welcome, Sign In to Your Account"}, "usernameLabel": "Username", "passwordLabel": "Password", "passwordShowTooltip": "Show Password", "passwordHideTooltip": "Hide Password", "submit": "Sign In", "error": {"loginFailed": "Login Failed!"}}}, "detailPage": {"title": "Detail Page"}}