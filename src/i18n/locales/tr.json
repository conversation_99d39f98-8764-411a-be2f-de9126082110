{"common": {"languages": {"tr": "Türkçe", "en": "English"}, "colorMode": {"light": "Aydınlık", "dark": "Karanlık", "system": "Sistem Te<PERSON>ı"}, "route": {"passwordChange": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap"}, "validation": {"usernameReq": "Lütfen kullanıcı adınızı giriniz.", "usernameMin": "Kullanıcı adınız en az {len} karakter olmalıdır.", "usernameMax": "Kullanıcı adınız en fazla {len} karakter olmalıdır.", "passwordReq": "Lütfen şifrenizi giriniz.", "passwordMin": "Şifreniz en az {len} karakter olmalıdır.", "passwordMax": "Şifreniz en fazla {len} karakter olmalıdır."}, "error": {"UNHANDLED_ERROR": "Beklenmedik bir hata <PERSON>.", "ERR_BAD_REQUEST": "İstek sırasında bir hata oluştu.", "asyncJobs": "İstek sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.", "captchaConfigCantFetched": "Captcha ayarları yüklenemedi!", "userConfigCantFetched": "Kullanıcı bilgileri yüklenemedi!", "loginUserNotFound": "Kullanıcı adı veya şifreniz hatalı."}, "tryAgainAction": "<PERSON><PERSON><PERSON>"}, "app": {"meta": {"title": {"template": "%s | TT Mobility Api Gateway UI", "default": "TT Mobility Api Gateway UI"}}}, "homepage": {"title": "<PERSON>"}, "loginPage": {"pageTitle": "<PERSON><PERSON><PERSON>", "form": {"header": {"mainTitle": "Mobility Api Gateway UI", "title": "Hoşgeldiniz, Hesabınıza Giriş Yapın"}, "usernameLabel": "Kullanıcı Adınız", "passwordLabel": "Şifreniz", "passwordShowTooltip": "Parolayı Göster", "passwordHideTooltip": "Parolayı Gizle", "submit": "<PERSON><PERSON><PERSON>", "error": {"loginFailed": "Giriş Başarısız!"}}}, "detailPage": {"title": "Detay Sayfası"}}