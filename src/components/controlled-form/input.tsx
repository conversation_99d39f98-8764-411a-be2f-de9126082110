import { Input } from '@heroui/react'
import { Controller, useFormContext } from 'react-hook-form'
import type { IControlledInputProps } from '@components/controlled-form/types'

const ControlledInput = ({ name, ...props }: IControlledInputProps) => {
  const {
    control,
    formState: { isSubmitting }
  } = useFormContext()

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <Input
          {...props}
          {...field}
          isDisabled={field.disabled ?? isSubmitting}
          isInvalid={!!error}
          errorMessage={error?.message}
        />
      )}
    />
  )
}

export default ControlledInput
