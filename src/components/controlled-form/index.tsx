import { Form } from '@heroui/react'
import { type FieldValues, FormProvider } from 'react-hook-form'
import type { IControlledFormProps } from '@components/controlled-form/types'

const ControlledForm = <FV extends FieldValues = FieldValues>({
  children,
  methods,
  ...formProps
}: IControlledFormProps<FV>) => (
  <FormProvider {...methods}>
    <Form {...formProps}>{children}</Form>
  </FormProvider>
)

export default ControlledForm
