import type { DetailedHTMLProps, FormHTMLAttributes } from 'react'
import type { FormProps, InputProps } from '@heroui/react'
import type {
  FieldValues,
  UseControllerProps,
  UseFormReturn
} from 'react-hook-form'

export interface IControlledFormProps<FV extends FieldValues = FieldValues>
  extends FormProps,
    Omit<
      DetailedHTMLProps<FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>,
      keyof FormProps
    > {
  children: Readonly<React.ReactNode>
  methods: UseFormReturn<FV>
}

export interface IControlledInputProps<FV extends FieldValues = FieldValues>
  extends UseControllerProps<FV>,
    Omit<InputProps, keyof UseControllerProps<FV>> {}
