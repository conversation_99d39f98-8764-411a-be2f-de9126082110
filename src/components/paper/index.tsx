import clsx from 'clsx'
import type { IPaperProps } from '@components/paper/types'
import styles from '@components/paper/styles.module'

const Paper = ({
  children,
  radius,
  shadow,
  spaces,
  className
}: IPaperProps) => (
  <div
    className={clsx(
      styles.paper,
      radius !== 'none' &&
        {
          sm: styles['-radius-sm'],
          md: styles['-radius-md'],
          lg: styles['-radius-lg']
        }[radius ?? 'md'],
      typeof shadow === 'object'
        ? [
            shadow.light !== 'none' &&
              {
                sm: styles['-shadow-sm'],
                md: styles['-shadow-md'],
                lg: styles['-shadow-lg']
              }[shadow.light],
            {
              sm: styles['-shadow-dark-sm'],
              md: styles['-shadow-dark-md'],
              lg: styles['-shadow-dark-lg'],
              none: styles['-shadow-dark-none']
            }[shadow.dark]
          ]
        : shadow !== 'none' &&
            {
              sm: styles['-shadow-sm'],
              md: styles['-shadow-md'],
              lg: styles['-shadow-lg']
            }[shadow ?? 'sm'],
      typeof spaces === 'object'
        ? [
            spaces.x !== 'none' &&
              {
                sm: styles['-spaces-x-sm'],
                md: styles['-spaces-x-md'],
                lg: styles['-spaces-x-lg']
              }[spaces.x],
            spaces.y !== 'none' &&
              {
                sm: styles['-spaces-y-sm'],
                md: styles['-spaces-y-md'],
                lg: styles['-spaces-y-lg']
              }[spaces.y]
          ]
        : spaces !== 'none' &&
            {
              sm: styles['-spaces-sm'],
              md: styles['-spaces-md'],
              lg: styles['-spaces-lg']
            }[spaces ?? 'md'],
      className
    )}>
    {children}
  </div>
)

export default Paper
