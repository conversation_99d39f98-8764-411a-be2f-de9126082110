import { yupResolver } from '@hookform/resolvers/yup'
import {
  type FieldValues,
  type UseFormProps,
  type UseFormReturn,
  useForm
} from 'react-hook-form'
import * as yup from 'yup'

interface IUseFormHookArgumentType<T extends FieldValues>
  extends UseFormProps<T> {
  validationRules?: { [R in keyof T]?: unknown }
}

export const useAppForm = <FV extends FieldValues>({
  ...options
}: IUseFormHookArgumentType<FV>): UseFormReturn<FV, object> =>
  useForm<FV>({
    mode: 'onSubmit',
    ...options,
    resolver:
      options.validationRules &&
      yupResolver(
        yup
          .object()
          .shape(options.validationRules as yup.ObjectShape)
          .required()
      )
  } as UseFormProps<FV>)

export default useAppForm
