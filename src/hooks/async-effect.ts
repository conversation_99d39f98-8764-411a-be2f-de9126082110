'use client'

import { type DependencyList, type EffectCallback, useEffect } from 'react'
import { addToast } from '@heroui/react'
import { useTranslations } from 'next-intl'

const useAsyncEffect = (
  effect: () => Promise<ReturnType<EffectCallback>>,
  deps: DependencyList = []
): ReturnType<typeof useEffect> => {
  const t = useTranslations()

  return useEffect(() => {
    effect().catch(() =>
      addToast({ color: 'danger', title: t('common.error.asyncJobs') })
    )
  }, deps)
}

export default useAsyncEffect
