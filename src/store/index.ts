import { combineReducers, configureStore } from '@reduxjs/toolkit'
import authReducer from '@reducers/auth-reducer'
import uiReducer from '@reducers/ui-reducer'
import userReducer from '@reducers/user-reducer'

type AppStore = typeof store
type AppState = ReturnType<AppStore['getState']>
type AppDispatch = AppStore['dispatch']
type AppReducer = typeof combinedReducer

const combinedReducer = combineReducers({
  ui: uiReducer,
  auth: authReducer,
  user: userReducer
})

const reducer: AppReducer = (state, action) => combinedReducer(state, action)
const store = configureStore({ reducer })

export {
  type AppDispatch,
  type AppReducer,
  type AppState,
  type AppStore,
  store
}
