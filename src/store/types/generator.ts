import type {
  ActionCreatorWithPreparedPayload,
  AsyncThunk,
  CaseReducer,
  GetThunkAPI,
  PayloadAction
} from '@reduxjs/toolkit'
import type { IErrorResponseModel } from '@responseTypes/error'
import type { KeyValuePair, ListOrderType } from '@types'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'

/* --------- Async Thunks --------- */
export type IActionParams = KeyValuePair

export type IRequestParameters = (
  | IActionParams
  | IRequestConfigs
  | (IActionParams & IRequestConfigs)
)[]

export interface IRequestConfigs extends AxiosRequestConfig {
  secure?: boolean
}

export interface IAsyncThunkInterEventProps<S = unknown> {
  options?: IAsyncThunkOptions<S>
  arg?: IAsyncThunkArg
  thunkActions: Omit<
    GetThunkAPI<object>,
    'extra' | 'requestId' | 'signal' | 'abort' | 'fulfillWithValue'
  >
}

export type IAsyncThunkInterEvent<S = unknown, E = unknown> = ({
  options,
  arg,
  thunkActions
}: IAsyncThunkInterEventProps<S> & E) => Promise<void> | void

export type IAsyncThunkArg =
  | (KeyValuePair & { queryString?: KeyValuePair<string | number | boolean> })
  | undefined

export interface IAsyncThunkOptions<S = unknown> {
  name?: string
  url: string
  method:
    | 'get'
    | 'delete'
    | 'head'
    | 'options'
    | 'post'
    | 'put'
    | 'patch'
    | 'postForm'
    | 'putForm'
    | 'patchForm'
  secure?: boolean
  segments?: string[]
  queries?: KeyValuePair<string | number>
  args?: string[]
  urlArgs?: KeyValuePair<string | ((arg: IAsyncThunkArg) => string)>
  onBeforeAll?: IAsyncThunkInterEvent<S>
  onBeforeRequest?: IAsyncThunkInterEvent<
    S,
    {
      actionParams?: IActionParams
      requestConfigs?: IRequestConfigs
    }
  >
  onAfterRequest?: IAsyncThunkInterEvent<
    S,
    { response?: AxiosResponse; data?: unknown }
  >
  onError?: IAsyncThunkInterEvent<S, { error: IAsyncThunkError }>
  cases?: {
    pending?: IExtraReducerCase<S>
    fulfilled?: IExtraReducerCase<S>
    rejected?: IExtraReducerCase<S>
  }
}

export type IAsyncThunk<R = unknown, A = IAsyncThunkArg> = AsyncThunk<
  R,
  A,
  object
>

export type IAsyncThunkItems = KeyValuePair<IAsyncThunk>

export type IAsyncThunkOptionList<S = unknown> = KeyValuePair<
  IAsyncThunkOptions<S>
>

/* --------- Static Actions --------- */
export interface IStaticActionOptions<S = unknown, P = unknown> {
  name?: string
  action?: (payload: unknown) => void
  case?: IExtraReducerCase<S, P>
}

export type IStaticAction<P = unknown> = ActionCreatorWithPreparedPayload<
  unknown[],
  P
>

export type IStaticActionItems = KeyValuePair<IStaticAction>

export type IStaticActionOptionList<S = unknown, P = unknown> = KeyValuePair<
  IStaticActionOptions<S, P>
>

/* --------- Extra Reducer Cases --------- */
export type IExtraReducerAsyncCaseTypes = 'pending' | 'fulfilled' | 'rejected'

export type IExtraReducerCase<S = unknown, P = unknown> = CaseReducer<
  S,
  PayloadAction<P>
>

/* --------- Error Handling --------- */
export interface IAsyncThunkError {
  status: number
  code: string | number
  message: string
  restError?: IErrorResponseModel
}

/* --------- Utils --------- */
export interface IPageableList<T = unknown> {
  totalCount: number
  currentPage: number
  currentSize: number
  list: T[]
}

export interface IPageablePayload<T = unknown> {
  pagination?: {
    totalCount?: number
    currentPage?: number
    currentSize?: number
  }
  list: T[]
}

export interface IOrderableList<T = unknown> {
  orders?: {
    orderBy: string
    orderAs: ListOrderType
  }
  list: T[]
}

export interface IOrderablePaylod<T = unknown> {
  filters?: {
    orderBy?: string
    orderAs?: ListOrderType
  }
  list: T[]
}
