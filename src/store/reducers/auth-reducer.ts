import { createSlice } from '@reduxjs/toolkit'
import { endpoints } from '@api/urls'
import { USER_IS_AUTH } from '@storage/keys'
import {
  getStorageValue,
  removeStorageItem,
  setStorageItem
} from '@storage/local'
import {
  generateAsyncThunks,
  generateExtraReducers,
  generateStaticActions
} from '@storeUtils/generator'
import type { ICaptchaResponseModel } from '@responseTypes/captcha-config'
import type { AppState } from '@store'
import type {
  IAsyncThunkOptionList,
  IStaticActionOptionList
} from '@storeTypes/generator'

export interface IAuthCaptcha {
  siteKey: string
  captchaEnabled: boolean
  isFetched: boolean
  isPending: boolean
  errors: string[]
}

export interface IAuthReducerState {
  step: 'init' | 'auth' | 'instant'
  isAuth: boolean
  isPending: boolean
  errors: string[]
  authSuccessEvent?: () => Promise<void>
  captcha: IAuthCaptcha
}

const isAuth = !!getStorageValue(USER_IS_AUTH)

const initialState: IAuthReducerState = {
  step: isAuth ? 'auth' : 'init',
  isAuth,
  isPending: false,
  errors: [],
  authSuccessEvent: undefined,
  captcha: {
    siteKey: '',
    captchaEnabled: false,
    isFetched: false,
    isPending: false,
    errors: []
  }
}

const asyncThunkList: IAsyncThunkOptionList<IAuthReducerState> = {
  captchaConfigGet: {
    url: endpoints.auth.captchaConfig,
    method: 'get',
    secure: false,
    onAfterRequest: ({ data, thunkActions: { dispatch } }) => {
      dispatch(clearCaptchaErrors())
      dispatch(setCaptchaConfig(data))
    },
    onError: ({ error, thunkActions: { dispatch } }) => {
      dispatch(clearCaptchaErrors())
      dispatch(addCaptchaError(error.message))
    },
    cases: {
      pending: ({ captcha }) => {
        captcha.isPending = true
        captcha.isFetched = false
      },
      fulfilled: ({ captcha }) => {
        captcha.isPending = false
        captcha.isFetched = true
      },
      rejected: ({ captcha }) => {
        captcha.isPending = false
        captcha.isFetched = false
      }
    }
  },
  loginInit: {
    url: endpoints.auth.login,
    method: 'post',
    secure: false,
    args: ['username', 'password', 'reCaptcha'],
    onAfterRequest: async ({ thunkActions: { dispatch, getState } }) => {
      dispatch(clearAuthErrors())
      dispatch(setAuth())
      await (getState() as AppState).auth.authSuccessEvent?.()
    },
    onError: ({ error, thunkActions: { dispatch } }) => {
      dispatch(clearAuthErrors())
      dispatch(addAuthError(error.message))
    },
    cases: {
      pending: (state) => {
        state.isPending = true
      },
      fulfilled: (state) => {
        state.isPending = false
      },
      rejected: (state) => {
        state.isPending = false
      }
    }
  }
}

const staticActionList: IStaticActionOptionList<IAuthReducerState> = {
  /* --------- captchaConfigGet --------- */
  setCaptchaConfig: {
    case: ({ captcha }, action) => {
      const { payload } = action as { payload: ICaptchaResponseModel }

      captcha.siteKey = payload.siteKey
      captcha.captchaEnabled = payload.captchaEnabled
    }
  },
  addCaptchaError: {
    case: ({ captcha }, action) => {
      const { payload } = action as { payload: string }

      captcha.errors = captcha.errors.concat(payload)
    }
  },
  clearCaptchaErrors: {
    case: ({ captcha }) => {
      captcha.errors = []
    }
  },
  resetCaptchaConfig: {
    case: (state) => {
      state.captcha = initialState.captcha
    }
  },
  /* --------- loginInit --------- */
  setAuth: {
    action: () => {
      setStorageItem(USER_IS_AUTH, true)
    },
    case: (state) => {
      state.step = 'auth'
      state.isAuth = true
    }
  },
  addAuthError: {
    case: (state, action) => {
      const { payload } = action as { payload: string }

      state.errors = state.errors.concat(payload)
    }
  },
  clearAuthErrors: {
    case: (state) => {
      state.errors = []
    }
  },
  resetAuth: {
    action: () => {
      removeStorageItem(USER_IS_AUTH)
    },
    case: (state) => {
      state.step = 'init'
      state.isAuth = false
      state.isPending = false
      state.authSuccessEvent = undefined
      state.errors = []
    }
  },
  /* --------- Reset --------- */
  resetAllAuth: {
    action: () => {
      removeStorageItem(USER_IS_AUTH)
    },
    case: (state) => {
      state.step = 'init'
      state.isAuth = false
      state.isPending = false
      state.authSuccessEvent = undefined
      state.errors = []
      state.captcha = initialState.captcha
    }
  }
}

const asyncThunks = generateAsyncThunks<IAuthReducerState>(
  'auth',
  asyncThunkList
)

const staticActions = generateStaticActions<IAuthReducerState>(
  'auth',
  staticActionList
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {},
  extraReducers: (builder) =>
    generateExtraReducers<IAuthReducerState>(
      builder,
      { asyncThunkList, asyncThunks },
      { staticActionList, staticActions }
    )
})

export const {
  /* --------- Async Thunks --------- */
  captchaConfigGet,
  loginInit,
  /* --------- Static Actions --------- */
  setCaptchaConfig,
  addCaptchaError,
  clearCaptchaErrors,
  resetCaptchaConfig,
  setAuth,
  addAuthError,
  clearAuthErrors,
  resetAuth,
  /* --------- Reset --------- */
  resetAllAuth,
  /* --------- Slice --------- */
  actions,
  selectAuthState
} = {
  /* --------- Async Thunks --------- */
  captchaConfigGet: asyncThunks.captchaConfigGet,
  loginInit: asyncThunks.loginInit,
  /* --------- Static Actions --------- */
  setCaptchaConfig: staticActions.setCaptchaConfig,
  addCaptchaError: staticActions.addCaptchaError,
  clearCaptchaErrors: staticActions.clearCaptchaErrors,
  resetCaptchaConfig: staticActions.resetCaptchaConfig,
  setAuth: staticActions.setAuth,
  addAuthError: staticActions.addAuthError,
  clearAuthErrors: staticActions.clearAuthErrors,
  resetAuth: staticActions.resetAuth,
  /* --------- Reset --------- */
  resetAllAuth: staticActions.resetAllAuth,
  /* --------- Slice --------- */
  actions: authSlice.actions,
  selectAuthState: (state: AppState) => state.auth
}

export default authSlice.reducer
