import { createSlice } from '@reduxjs/toolkit'
import {
  generateExtraReducers,
  generateStaticActions
} from '@storeUtils/generator'
import type { AppState } from '@store'
import type { IStaticActionOptionList } from '@storeTypes/generator'

export interface IUiReducerState {
  loader: {
    isOpen: boolean
    type?: 'splash-solid' | 'splash-transparent'
  }
}

const initialState: IUiReducerState = {
  loader: {
    isOpen: false,
    type: 'splash-solid'
  }
}

const staticActionList: IStaticActionOptionList<IUiReducerState> = {
  setLoader: {
    case: (state, action) => {
      const { payload } = action as { payload: IUiReducerState['loader'] }

      state.loader.isOpen = payload.isOpen
      state.loader.type = payload.type ?? 'splash-solid'
    }
  },
  resetLoader: {
    case: (state) => {
      state.loader = initialState.loader
    }
  }
}

const staticActions = generateStaticActions<IUiReducerState>(
  'ui',
  staticActionList
)

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {},
  extraReducers: (builder) =>
    generateExtraReducers<IUiReducerState>(
      builder,
      {},
      { staticActionList, staticActions }
    )
})

export const {
  /* --------- Static Actions --------- */
  setLoader,
  resetLoader,
  /* --------- Slice --------- */
  actions,
  selectUiState
} = {
  /* --------- Static Actions --------- */
  setLoader: staticActions.setLoader,
  resetLoader: staticActions.resetLoader,
  /* --------- Slice --------- */
  actions: uiSlice.actions,
  selectUiState: (state: AppState) => state.ui
}

export default uiSlice.reducer
