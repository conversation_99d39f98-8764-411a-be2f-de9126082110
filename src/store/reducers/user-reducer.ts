import { createSlice } from '@reduxjs/toolkit'
import { endpoints } from '@api/urls'
import {
  generateAsyncThunks,
  generateExtraReducers,
  generateStaticActions
} from '@storeUtils/generator'
import type { ICurrentUserResponseModel } from '@responseTypes/current-user'
import type { AppState } from '@store'
import type {
  IAsyncThunkOptionList,
  IStaticActionOptionList
} from '@storeTypes/generator'

export interface IUserReducerState {
  user: {
    data?: {
      id: number
      firstName: string
      lastName: string
      email: string
      privileges: string[]
    }
    isFetched: boolean
    isPending: boolean
    errors: string[]
  }
}

const initialState: IUserReducerState = {
  user: {
    isFetched: false,
    isPending: false,
    errors: []
  }
}

const asyncThunkList: IAsyncThunkOptionList<IUserReducerState> = {
  userDataGet: {
    url: endpoints.user.me,
    method: 'get',
    secure: true,
    onAfterRequest: ({ data, thunkActions: { dispatch } }) => {
      dispatch(clearUserErrors())
      dispatch(setUserData(data))
    },
    onError: ({ error, thunkActions: { dispatch } }) => {
      dispatch(clearUserErrors())
      dispatch(addUserError(error.message))
    },
    cases: {
      pending: ({ user }) => {
        user.isPending = true
        user.isFetched = false
      },
      fulfilled: ({ user }) => {
        user.isPending = false
        user.isFetched = true
      },
      rejected: ({ user }) => {
        user.isPending = false
        user.isFetched = false
      }
    }
  }
}

const staticActionList: IStaticActionOptionList<IUserReducerState> = {
  /* --------- userDataGet --------- */
  setUserData: {
    case: ({ user }, action) => {
      const { payload } = action as { payload: ICurrentUserResponseModel }

      user.data = {
        id: payload.id,
        firstName: payload.firstName,
        lastName: payload.lastName,
        email: payload.email,
        privileges: payload.privileges
      }
    }
  },
  addUserError: {
    case: ({ user }, action) => {
      const { payload } = action as { payload: string }

      user.errors = user.errors.concat(payload)
    }
  },
  clearUserErrors: {
    case: ({ user }) => {
      user.errors = []
    }
  },
  resetUserData: {
    case: (state) => {
      state.user = initialState.user
    }
  },
  /* --------- Reset --------- */
  resetAllUser: {
    case: (state) => {
      state.user = initialState.user
    }
  }
}

const asyncThunks = generateAsyncThunks<IUserReducerState>(
  'user',
  asyncThunkList
)

const staticActions = generateStaticActions<IUserReducerState>(
  'user',
  staticActionList
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) =>
    generateExtraReducers<IUserReducerState>(
      builder,
      { asyncThunkList, asyncThunks },
      { staticActionList, staticActions }
    )
})

export const {
  /* --------- userDataGet --------- */
  userDataGet,
  /* --------- Static Actions --------- */
  setUserData,
  addUserError,
  clearUserErrors,
  resetUserData,
  /* --------- Reset --------- */
  resetAllUser,
  /* --------- Slice --------- */
  actions,
  selectUserState
} = {
  /* --------- Async Thunks --------- */
  userDataGet: asyncThunks.userDataGet,
  /* --------- Static Actions --------- */
  setUserData: staticActions.setUserData,
  addUserError: staticActions.addUserError,
  clearUserErrors: staticActions.clearUserErrors,
  resetUserData: staticActions.resetUserData,
  /* --------- Reset --------- */
  resetAllUser: staticActions.resetAllUser,
  /* --------- Slice --------- */
  actions: userSlice.actions,
  selectUserState: (state: AppState) => state.user
}

export default userSlice.reducer
