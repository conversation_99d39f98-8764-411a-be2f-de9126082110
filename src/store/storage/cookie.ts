'use server'

import { cookies as getCookies } from 'next/headers'
import type {
  ICookieGetter,
  ICookieRemover,
  ICookieSetter
} from '@storage/types'

export const getCookieValue = async <T = string | boolean | number | object>(
  key: string,
  defaultValue?: T
): ICookieGetter<T> => {
  const cookieValue = (await getCookies()).get(key)?.value

  return cookieValue ? (JSON.parse(cookieValue) as T) : defaultValue
}

export const setCookieItem: ICookieSetter = async (key, value) => {
  const cookies = await getCookies()

  cookies.set(key, JSON.stringify(value))
}

export const removeCookieItem: ICookieRemover = async (key) => {
  const cookies = await getCookies()

  cookies.delete(key)
}
