export type ILocalStorageGetter<T> = T | undefined

export type ILocalStorageSetter = (
  key: string,
  value: string | boolean | number | object
) => void

export type ILocalStorageRemover = (key: string) => void

export type ICookieGetter<T> = Promise<T | undefined>

export type ICookieSetter = (
  key: string,
  value: string | boolean | number | object
) => Promise<void>

export type ICookieRemover = (key: string) => Promise<void>
