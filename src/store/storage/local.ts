'use client'

import type {
  ILocalStorageGetter,
  ILocalStorageRemover,
  ILocalStorageSetter
} from '@storage/types'

export const getStorageValue = <T = string | boolean | number | object>(
  key: string,
  defaultValue?: T
): ILocalStorageGetter<T> => {
  const localValue = typeof window !== 'undefined' && localStorage.getItem(key)

  return localValue ? (JSON.parse(localValue) as T) : defaultValue
}

export const setStorageItem: ILocalStorageSetter = (key, value) =>
  typeof window !== 'undefined' &&
  localStorage.setItem(key, JSON.stringify(value))

export const removeStorageItem: ILocalStorageRemover = (key) =>
  typeof window !== 'undefined' && localStorage.removeItem(key)
