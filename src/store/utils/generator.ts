import {
  type ActionReducerMapBuilder,
  createAction,
  createAsyncThunk
} from '@reduxjs/toolkit'
import { type AxiosError } from 'axios'
import { getProperty } from 'dot-prop'
import axios from '@api/axios'
import { apiUrl } from '@api/urls'
import { getTranslationMessages } from '@i18n/services'
import type { IErrorResponseModel } from '@responseTypes/error'
import type {
  IActionParams,
  IAsyncThunk,
  IAsyncThunkError,
  IAsyncThunkItems,
  IAsyncThunkOptionList,
  IAsyncThunkOptions,
  IExtraReducerAsyncCaseTypes,
  IRequestConfigs,
  IRequestParameters,
  IStaticAction,
  IStaticActionItems,
  IStaticActionOptionList,
  IStaticActionOptions
} from '@storeTypes/generator'
import type { KeyValuePair } from '@types'

/* --------- Async Thunks --------- */
export const generateAsyncThunk = <S = unknown>(
  reducerName: string,
  options: IAsyncThunkOptions<S>
): IAsyncThunk =>
  createAsyncThunk(
    `${reducerName}/${options.name}`,
    async (arg, { dispatch, rejectWithValue, getState }) => {
      const requestUrlArgs: KeyValuePair<string> = {}
      const actionParams: IActionParams = {}
      const requestConfigs: IRequestConfigs = {}
      const requestParameters: IRequestParameters = []

      await options.onBeforeAll?.({
        options,
        arg,
        thunkActions: { dispatch, rejectWithValue, getState }
      })

      if (options.urlArgs)
        Object.entries(options.urlArgs).forEach(
          ([oKey, oVal]) =>
            (requestUrlArgs[oKey] =
              typeof oVal === 'function' ? oVal(arg) : oVal)
        )

      const requestQuerystring = { ...options.queries, ...arg?.queryString }

      delete arg?.queryString
      options.args?.forEach((argKey) => (actionParams[argKey] = arg?.[argKey]))

      if (options.secure !== undefined) requestConfigs.secure = options.secure

      await options.onBeforeRequest?.({
        options,
        arg,
        actionParams,
        requestConfigs,
        thunkActions: { dispatch, rejectWithValue, getState }
      })

      if (
        ['post', 'put', 'patch', 'postForm', 'putForm', 'patchForm'].includes(
          options.method
        )
      )
        requestParameters.push(actionParams)

      if (Object.keys(requestConfigs).length)
        requestParameters.push(requestConfigs)

      try {
        const response = await axios[options.method](
          apiUrl({
            path: options.url,
            segments: options.segments ?? [],
            queries: requestQuerystring,
            args: requestUrlArgs
          }),
          ...requestParameters
        )

        await options.onAfterRequest?.({
          options,
          arg,
          response,
          data: response.data,
          thunkActions: { dispatch, rejectWithValue, getState }
        })

        return response.data?.response
      } catch (exception) {
        const asyncThunkError = await errorHandler(
          exception as AxiosError<IErrorResponseModel>
        )

        await options.onError?.({
          options,
          arg,
          error: asyncThunkError,
          thunkActions: { dispatch, rejectWithValue, getState }
        })

        return rejectWithValue(asyncThunkError)
      }
    }
  )

export const generateAsyncThunks = <S = unknown>(
  reducerName: string,
  asyncThunkOptionList: IAsyncThunkOptionList<S>
): IAsyncThunkItems => {
  const asyncThunks: IAsyncThunkItems = {}

  Object.keys(asyncThunkOptionList).forEach((thunkName) => {
    asyncThunks[thunkName] = generateAsyncThunk<S>(reducerName, {
      name: thunkName,
      ...asyncThunkOptionList[thunkName]
    })
  })

  return asyncThunks
}

/* --------- Static Actions --------- */
export const generateStaticAction = <S = unknown>(
  reducerName: string,
  options: IStaticActionOptions<S>
): IStaticAction =>
  createAction(`${reducerName}/${options.name}`, (payload: unknown) => {
    options.action?.(payload)

    return { payload }
  })

export const generateStaticActions = <S = unknown>(
  reducerName: string,
  staticActionOptionList: IStaticActionOptionList<S>
): IStaticActionItems => {
  const staticActions: IStaticActionItems = {}

  Object.keys(staticActionOptionList).forEach((actionName) => {
    staticActions[actionName] = generateStaticAction<S>(reducerName, {
      name: actionName,
      ...staticActionOptionList[actionName]
    })
  })

  return staticActions
}

/* --------- Extra Reducer Cases --------- */
export const generateExtraReducers = <S = unknown>(
  builder: ActionReducerMapBuilder<S>,
  {
    asyncThunkList,
    asyncThunks
  }: {
    asyncThunkList?: IAsyncThunkOptionList<S>
    asyncThunks?: IAsyncThunkItems
  },
  {
    staticActionList,
    staticActions
  }: {
    staticActionList?: IStaticActionOptionList<S>
    staticActions?: IStaticActionItems
  }
): void => {
  const asyncCaseTypes: IExtraReducerAsyncCaseTypes[] = [
    'pending',
    'fulfilled',
    'rejected'
  ]

  if (asyncThunkList && asyncThunks)
    Object.keys(asyncThunkList).forEach((thunkName) =>
      asyncCaseTypes.forEach((caseType) => {
        const thunkCase = asyncThunkList[thunkName].cases?.[caseType]

        if (thunkCase)
          builder.addCase(asyncThunks[thunkName][caseType], thunkCase)
      })
    )

  if (staticActionList)
    Object.keys(staticActionList).forEach((actionKey) => {
      const actionCase = staticActionList[actionKey].case

      if (actionCase && staticActions)
        builder.addCase(staticActions[actionKey], actionCase)
    })
}

/* --------- Error Handling --------- */
export const errorHandler = async (
  exception: AxiosError<IErrorResponseModel>
): Promise<IAsyncThunkError> => {
  const { code, message: exceptionMessage, response } = exception
  const messages = await getTranslationMessages()

  return {
    status: response?.status ?? 500,
    code: response?.data.code ?? code ?? 'UNHANDLED_ERROR',
    message: response?.data.code
      ? getProperty(messages.common, response.data.code, response.data.code)!
      : (getProperty(
          messages.common,
          `error.${code ?? 'UNHANDLED_ERROR'}`,
          getProperty(messages.common, 'error.UNHANDLED_ERROR')
        ) ??
        (exceptionMessage || 'UNHANDLED_ERROR')),
    restError: response?.data
  }
}
