@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 200;
  src: url('Mulish-ExtraLight.eot');
  src:
    local('Mulish-ExtraLight'),
    local('Mulish ExtraLight'),
    url('Mulish-ExtraLight.eot?#iefix') format('embedded-opentype'),
    url('Mulish-ExtraLight.woff2') format('woff2'),
    url('Mulish-ExtraLight.woff') format('woff'),
    url('Mulish-ExtraLight.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 200;
  src: url('Mulish-ExtraLight-Italic.eot');
  src:
    local('Mulish-ExtraLight-Italic'),
    local('Mulish ExtraLight Italic'),
    url('Mulish-ExtraLight-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-ExtraLight-Italic.woff2') format('woff2'),
    url('Mulish-ExtraLight-Italic.woff') format('woff'),
    url('Mulish-ExtraLight-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 300;
  src: url('Mulish-Light.eot');
  src:
    local('Mulish-Light'),
    local('Mulish Light'),
    url('Mulish-Light.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Light.woff2') format('woff2'),
    url('Mulish-Light.woff') format('woff'),
    url('Mulish-Light.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 300;
  src: url('Mulish-Light-Italic.eot');
  src:
    local('Mulish-Light-Italic'),
    local('Mulish Light Italic'),
    url('Mulish-Light-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Light-Italic.woff2') format('woff2'),
    url('Mulish-Light-Italic.woff') format('woff'),
    url('Mulish-Light-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 400;
  src: url('Mulish-Regular.eot');
  src:
    local('Mulish-Regular'),
    local('Mulish Regular'),
    url('Mulish-Regular.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Regular.woff2') format('woff2'),
    url('Mulish-Regular.woff') format('woff'),
    url('Mulish-Regular.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 400;
  src: url('Mulish-Regular-Italic.eot');
  src:
    local('Mulish-Regular-Italic'),
    local('Mulish Regular Italic'),
    url('Mulish-Regular-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Regular-Italic.woff2') format('woff2'),
    url('Mulish-Regular-Italic.woff') format('woff'),
    url('Mulish-Regular-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 500;
  src: url('Mulish-Medium.eot');
  src:
    local('Mulish-Medium'),
    local('Mulish Medium'),
    url('Mulish-Medium.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Medium.woff2') format('woff2'),
    url('Mulish-Medium.woff') format('woff'),
    url('Mulish-Medium.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 500;
  src: url('Mulish-Medium-Italic.eot');
  src:
    local('Mulish-Medium-Italic'),
    local('Mulish Medium Italic'),
    url('Mulish-Medium-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Medium-Italic.woff2') format('woff2'),
    url('Mulish-Medium-Italic.woff') format('woff'),
    url('Mulish-Medium-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 600;
  src: url('Mulish-SemiBold.eot');
  src:
    local('Mulish-SemiBold'),
    local('Mulish SemiBold'),
    url('Mulish-SemiBold.eot?#iefix') format('embedded-opentype'),
    url('Mulish-SemiBold.woff2') format('woff2'),
    url('Mulish-SemiBold.woff') format('woff'),
    url('Mulish-SemiBold.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 600;
  src: url('Mulish-SemiBold-Italic.eot');
  src:
    local('Mulish-SemiBold-Italic'),
    local('Mulish SemiBold Italic'),
    url('Mulish-SemiBold-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-SemiBold-Italic.woff2') format('woff2'),
    url('Mulish-SemiBold-Italic.woff') format('woff'),
    url('Mulish-SemiBold-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 700;
  src: url('Mulish-Bold.eot');
  src:
    local('Mulish-Bold'),
    local('Mulish Bold'),
    url('Mulish-Bold.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Bold.woff2') format('woff2'),
    url('Mulish-Bold.woff') format('woff'),
    url('Mulish-Bold.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 700;
  src: url('Mulish-Bold-Italic.eot');
  src:
    local('Mulish-Bold-Italic'),
    local('Mulish Bold Italic'),
    url('Mulish-Bold-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Bold-Italic.woff2') format('woff2'),
    url('Mulish-Bold-Italic.woff') format('woff'),
    url('Mulish-Bold-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 800;
  src: url('Mulish-ExtraBold.eot');
  src:
    local('Mulish-ExtraBold'),
    local('Mulish ExtraBold'),
    url('Mulish-ExtraBold.eot?#iefix') format('embedded-opentype'),
    url('Mulish-ExtraBold.woff2') format('woff2'),
    url('Mulish-ExtraBold.woff') format('woff'),
    url('Mulish-ExtraBold.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 800;
  src: url('Mulish-ExtraBold-Italic.eot');
  src:
    local('Mulish-ExtraBold-Italic'),
    local('Mulish ExtraBold Italic'),
    url('Mulish-ExtraBold-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-ExtraBold-Italic.woff2') format('woff2'),
    url('Mulish-ExtraBold-Italic.woff') format('woff'),
    url('Mulish-ExtraBold-Italic.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: normal;
  font-weight: 900;
  src: url('Mulish-Black.eot');
  src:
    local('Mulish-Black'),
    local('Mulish Black'),
    url('Mulish-Black.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Black.woff2') format('woff2'),
    url('Mulish-Black.woff') format('woff'),
    url('Mulish-Black.ttf') format('truetype');
}

@font-face {
  font-display: swap;
  font-family: Mulish;
  font-style: italic;
  font-weight: 900;
  src: url('Mulish-Black-Italic.eot');
  src:
    local('Mulish-Black-Italic'),
    local('Mulish Black Italic'),
    url('Mulish-Black-Italic.eot?#iefix') format('embedded-opentype'),
    url('Mulish-Black-Italic.woff2') format('woff2'),
    url('Mulish-Black-Italic.woff') format('woff'),
    url('Mulish-Black-Italic.ttf') format('truetype');
}
