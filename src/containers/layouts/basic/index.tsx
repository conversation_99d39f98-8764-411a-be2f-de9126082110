'use server'

import { Navbar, Nav<PERSON><PERSON><PERSON>, Navbar<PERSON>ontent, NavbarItem } from '@heroui/react'
import ColorModeSwitcher from '@partials/color-mode-switcher'
import LanguageSwitcher from '@partials/language-switcher'
import { Logo } from '@partials/logo'
import type { IBasicLayoutProps } from '@layouts/basic/types'
import styles from '@layouts/basic/styles.module'

const BasicLayout = ({ children }: IBasicLayoutProps) => (
  <div className={styles.layout}>
    <Navbar
      isBordered
      isBlurred={false}
      className={styles.navbar}
      classNames={{
        wrapper: styles.navbar__wrapper,
        brand: styles.navbar__brand,
        content: styles.navbar__content
      }}>
      <NavbarBrand>
        <Logo className={styles.navbar__logo} />
      </NavbarBrand>
      <NavbarContent justify='end'>
        <NavbarItem>
          <LanguageSwitcher />
        </NavbarItem>
        <NavbarItem>
          <ColorModeSwitcher />
        </NavbarItem>
      </NavbarContent>
    </Navbar>
    <main className={styles.main}>{children}</main>
  </div>
)

export default BasicLayout
