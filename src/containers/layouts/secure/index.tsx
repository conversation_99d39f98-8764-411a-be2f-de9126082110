'use server'

import { But<PERSON>, Navbar, Navbar<PERSON>ontent, NavbarItem } from '@heroui/react'
import MenuCloseIcon from '@icons/menu-close.svg'
import MenuOpenIcon from '@icons/menu-open.svg'
import SecureHeading from '@layouts/secure/heading'
import ColorModeSwitcher from '@partials/color-mode-switcher'
import LanguageSwitcher from '@partials/language-switcher'
import ProfilePopover from '@partials/profile-popover'
import type { ISecureLayoutProps } from '@layouts/secure/types'
import styles from '@layouts/secure/styles.module'

const SecureLayout = ({ children }: ISecureLayoutProps) => {
  const isOpen = 'asdasdas'.includes('a')

  return (
    <div className={styles.layout}>
      <div className={styles.sidebar}>sidebar</div>
      <div className={styles.content}>
        <div className={styles.heading}>
          <Navbar
            position='static'
            isBlurred={false}
            className={styles['heading-navbar']}
            classNames={{
              wrapper: styles['heading-navbar__wrapper'],
              content: styles['heading-navbar__content']
            }}>
            <NavbarContent>
              <NavbarItem>
                <Button isIconOnly radius='full' variant='light' size='lg'>
                  {isOpen ? (
                    <MenuCloseIcon
                      className={styles['sidebar-trigger__icon']}
                    />
                  ) : (
                    <MenuOpenIcon className={styles['sidebar-trigger__icon']} />
                  )}
                </Button>
              </NavbarItem>
            </NavbarContent>
            <NavbarContent justify='end'>
              <NavbarItem>
                <LanguageSwitcher />
              </NavbarItem>
              <NavbarItem>
                <ColorModeSwitcher />
              </NavbarItem>
              <NavbarItem>
                <ProfilePopover />
              </NavbarItem>
            </NavbarContent>
          </Navbar>
        </div>
        <main className={styles.main}>
          <SecureHeading title='Home Page' />
          <div className={styles['main-content']}>{children}</div>
        </main>
      </div>
    </div>
  )
}

export default SecureLayout
