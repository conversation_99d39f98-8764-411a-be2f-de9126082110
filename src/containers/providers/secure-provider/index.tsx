'use client'

import { useEffect, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { addToast, closeAll } from '@heroui/react'
import { useTranslations } from 'next-intl'
import useAsyncEffect from '@hooks/async-effect'
import SplashScreen from '@partials/splash-screen'
import { resetAllAuth, selectAuthState } from '@reducers/auth-reducer'
import {
  resetAllUser,
  selectUserState,
  userDataGet
} from '@reducers/user-reducer'
import { routes, urlReturnQuery } from '@settings'
import { useAppDispatch, useAppSelector } from '@store/client'
import type { ISecureProviderProps } from '@providers/secure-provider/types'

const SecureProvider = ({ children }: ISecureProviderProps) => {
  const t = useTranslations('common')
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const dispatch = useAppDispatch()
  const { isAuth } = useAppSelector(selectAuthState)
  const { user } = useAppSelector(selectUserState)
  const [isChecking, setIsChecking] = useState<boolean>(true)

  const getLoginUrl = () => {
    const params = new URLSearchParams(searchParams)

    if (pathname !== routes.secure.homepage)
      params.set(urlReturnQuery, pathname)

    const queryString = params.toString()

    return `${routes.guest.login}${queryString && '?'}${queryString}`
  }

  useAsyncEffect(async () => {
    if (isAuth) {
      closeAll()

      if (user.isFetched) setIsChecking(false)
      else if (!user.isPending) await dispatch(userDataGet())
    } else router.replace(getLoginUrl())
  }, [isAuth])

  useEffect(() => {
    if (!user.isPending) {
      if (user.isFetched) setIsChecking(false)

      if (user.errors.length) {
        closeAll()

        user.errors.forEach((error) => {
          addToast({
            color: 'danger',
            title: t('error.userConfigCantFetched'),
            description: error
          })
        })

        dispatch(resetAllAuth())
        dispatch(resetAllUser())
      }
    }
  }, [user.isPending, user.isFetched, user.errors])

  return isChecking ? <SplashScreen /> : children
}

export default SecureProvider
