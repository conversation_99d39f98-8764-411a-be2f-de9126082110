'use client'

import { Hero<PERSON><PERSON>rovider, ToastProvider } from '@heroui/react'
import { NextIntlClientProvider } from 'next-intl'
import { ThemeProvider } from 'next-themes'
import { Provider as ReduxProvider } from 'react-redux'
import { providerMessageFallback, providerOnError } from '@i18n/services'
import LoaderScreenProvider from '@providers/loader-screen-provider'
import ProgressBarProvider from '@providers/progress-bar-provider'
import { APP_CURRENT_COLOR_MODE } from '@storage/keys'
import { store } from '@store'
import type { IAppProvidersProps } from '@providers/app-providers/types'

const AppProviders = ({ locale, messages, children }: IAppProvidersProps) => (
  <ReduxProvider store={store}>
    <NextIntlClientProvider
      locale={locale}
      messages={messages}
      getMessageFallback={providerMessageFallback}
      onError={providerOnError}>
      <HeroUIProvider locale={locale}>
        <ThemeProvider attribute='class' storageKey={APP_CURRENT_COLOR_MODE}>
          <ProgressBarProvider>
            <ToastProvider
              toastProps={{
                variant: 'bordered',
                shouldShowTimeoutProgress: true,
                timeout: 10000
              }}
            />
            <LoaderScreenProvider>{children}</LoaderScreenProvider>
          </ProgressBarProvider>
        </ThemeProvider>
      </HeroUIProvider>
    </NextIntlClientProvider>
  </ReduxProvider>
)

export default AppProviders
