import SplashScreen from '@partials/splash-screen'
import { selectUiState } from '@reducers/ui-reducer'
import { useAppSelector } from '@store/client'
import type { ILoaderScreenProviderProps } from '@providers/loader-screen-provider/types'

const LoaderScreenProvider = ({ children }: ILoaderScreenProviderProps) => {
  const { loader } = useAppSelector(selectUiState)

  return (
    <>
      {loader.isOpen && <SplashScreen type={loader.type} />}
      {children}
    </>
  )
}

export default LoaderScreenProvider
