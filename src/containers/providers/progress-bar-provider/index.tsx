import { ProgressProvider } from '@bprogress/next/app'
import '@providers/progress-bar-provider/styles'
import type { IProgressBarProviderProps } from '@providers/progress-bar-provider/types'

const ProgressBarProvider = ({ children }: IProgressBarProviderProps) => (
  <ProgressProvider
    shallowRouting
    disableSameURL={false}
    options={{ easing: 'ease-in-out', showSpinner: false }}>
    {children}
  </ProgressProvider>
)

export default ProgressBarProvider
