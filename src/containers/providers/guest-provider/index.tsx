'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import SplashScreen from '@partials/splash-screen'
import { selectAuthState } from '@reducers/auth-reducer'
import { routes, urlReturnQuery } from '@settings'
import { useAppSelector } from '@store/client'
import type { IGuestProviderProps } from '@providers/guest-provider/types'

const GuestProvider = ({ children }: IGuestProviderProps) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isAuth } = useAppSelector(selectAuthState)
  const [isChecking, setIsChecking] = useState<boolean>(true)

  useEffect(() => {
    if (isAuth) {
      setIsChecking(true)
      router.replace(searchParams.get(urlReturnQuery) ?? routes.secure.homepage)
    } else setIsChecking(false)
  }, [isAuth])

  return isChecking ? <SplashScreen /> : children
}

export default GuestProvider
