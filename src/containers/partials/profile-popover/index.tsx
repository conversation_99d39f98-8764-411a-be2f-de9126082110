'use client'

import {
  <PERSON><PERSON>,
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
  User
} from '@heroui/react'
import { useTranslations } from 'next-intl'
import KeyIcon from '@icons/key.svg'
import LogoutIcon from '@icons/logout.svg'
import { resetAllAuth } from '@reducers/auth-reducer'
import { resetAllUser, selectUserState } from '@reducers/user-reducer'
import { routes } from '@settings'
import { useAppDispatch, useAppSelector } from '@store/client'
import styles from '@partials/profile-popover/styles.module'

const ProfilePopover = () => {
  const t = useTranslations('common.route')
  const dispatch = useAppDispatch()
  const { user } = useAppSelector(selectUserState)

  const userIdentInitials = [user.data?.firstName, user.data?.lastName]
    .map((val) => val?.charAt(0).toUpperCase())
    .join('')

  return user.isFetched ? (
    <Dropdown
      offset={2}
      placement='bottom-end'
      classNames={{ content: styles.dropdown__content }}>
      <DropdownTrigger>
        <Button isIconOnly radius='full' variant='light' size='lg'>
          <Avatar isBordered name={userIdentInitials} size='sm' />
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        variant='flat'
        itemClasses={{
          base: styles.item,
          title: styles.item__content
        }}>
        <DropdownSection showDivider>
          <DropdownItem key='account' href={routes.secure.account}>
            <User
              avatarProps={{ name: userIdentInitials }}
              classNames={{
                base: styles.user,
                name: styles.user__name,
                description: styles.user__email
              }}
              name={[user.data?.firstName, user.data?.lastName].join(' ')}
              description={user.data?.email}
            />
          </DropdownItem>
        </DropdownSection>
        <DropdownItem
          key='passwordChange'
          href={routes.secure.passwordChange}
          startContent={<KeyIcon className={styles.item__icon} />}>
          {t('passwordChange')}
        </DropdownItem>
        <DropdownItem
          key='logout'
          color='danger'
          className={styles['-logout']}
          startContent={<LogoutIcon className={styles.item__icon} />}
          onPress={() => {
            dispatch(resetAllAuth())
            dispatch(resetAllUser())
          }}>
          {t('logout')}
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  ) : (
    <Button
      isLoading
      isDisabled
      isIconOnly
      radius='full'
      variant='light'
      size='lg'
    />
  )
}

export default ProfilePopover
