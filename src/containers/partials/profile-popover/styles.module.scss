.dropdown {
  &__content {
    @apply border border-default-200 bg-gradient-to-br from-primary/15 to-secondary/20 dark:from-primary/10 dark:to-secondary/10;
  }
}

.trigger {
  &__icon {
    @apply h-6.5 w-6.5;
  }
}

.item {
  @apply gap-3 px-3 py-2.5;

  &__content {
    @apply flex items-center justify-start gap-2;
  }

  &__icon {
    @apply h-5 w-5;
  }

  &.-logout {
    @apply text-danger;
  }
}

.user {
  @apply gap-3;

  &__name {
    @apply text-sm font-semibold text-foreground;
  }

  &__email {
    @apply text-xs text-foreground/70;
  }
}
