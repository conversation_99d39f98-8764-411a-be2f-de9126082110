'use client'

import { useTransition } from 'react'
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger
} from '@heroui/react'
import { useLocale, useTranslations } from 'next-intl'
import { setLanguage } from '@i18n/services'
import { type LanguageSlug, languageSlugs } from '@i18n/settings'
import { EnglishFlag, TurkishFlag } from '@icons/language-flags'
import TranslateIcon from '@icons/translate.svg'
import styles from '@partials/language-switcher/styles.module'

const LanguageSwitcher = () => {
  const t = useTranslations('common.languages')
  const locale = useLocale() as LanguageSlug
  const [languageIsChanging, languageChangeTransition] = useTransition()

  const languageFlag = (slug: string, className: string) =>
    ({
      en: <EnglishFlag className={className} />,
      tr: <TurkishFlag className={className} />
    })[slug] ?? <TranslateIcon className={className} />

  return (
    <Dropdown
      offset={2}
      placement='bottom-end'
      isDisabled={languageIsChanging}
      classNames={{ content: styles.dropdown__content }}>
      <DropdownTrigger>
        <Button isIconOnly radius='full' variant='light' size='lg'>
          {languageFlag(locale, styles.trigger__flag)}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disabledKeys={[locale]}
        variant='flat'
        itemClasses={{
          base: styles.item,
          title: styles.item__content
        }}>
        {languageSlugs.map((slug) => (
          <DropdownItem
            key={slug}
            textValue={slug}
            startContent={languageFlag(slug, styles.item__flag)}
            onPress={() =>
              languageChangeTransition(async () => await setLanguage(slug))
            }>
            {t(slug)}
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  )
}

export default LanguageSwitcher
