import { Spinner } from '@heroui/react'
import clsx from 'clsx'
import { useScrollLock } from 'usehooks-ts'
import { Logo } from '@partials/logo'
import type { ISplashScreenProps } from '@partials/splash-screen/types'
import styles from '@partials/splash-screen/styles.module'

const SplashScreen = ({ type }: ISplashScreenProps) => {
  useScrollLock()

  return (
    <div
      className={clsx(
        styles['splash-screen'],
        {
          'splash-solid': styles['-solid'],
          'splash-transparent': styles['-transparent']
        }[type ?? 'splash-solid']
      )}>
      <div className={styles['splash-screen__inner']}>
        <Logo className={styles.logo} />
        <Spinner
          className={styles.spinner}
          classNames={{ wrapper: styles.spinner__wrapper }}
          variant='gradient'
        />
      </div>
    </div>
  )
}

export default SplashScreen
