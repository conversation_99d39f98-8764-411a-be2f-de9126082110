import * as yup from 'yup'
import { type ILoginFormValidationRules } from '@partials/login-form/types'

export const validationRules: ILoginFormValidationRules = ({ t }) => ({
  username: yup
    .string()
    .required(t('usernameReq'))
    .min(3, t('usernameMin', { len: 3 }))
    .max(255, t('usernameMax', { len: 255 })),
  password: yup
    .string()
    .required(t('passwordReq'))
    .min(3, t('passwordMin', { len: 3 }))
    .max(255, t('passwordMax', { len: 255 }))
})
