.login {
  @apply flex flex-col gap-5;
  @apply md:gap-6;
}

.header {
  @apply flex flex-col items-center gap-4;
  @apply md:gap-6;

  &-main {
    @apply flex w-full flex-col items-center gap-1 border-b border-b-divider px-4 pb-4;
    @apply md:flex-row md:justify-between md:gap-6 md:pb-6;

    &__title {
      @apply text-center text-base font-medium;
      @apply md:text-right;
    }

    &__logo {
      @apply h-12;
    }
  }

  &__title {
    @apply px-4 text-center text-lg font-semibold;
  }
}

.form {
  @apply gap-5;
  @apply md:gap-6 md:px-4 md:pb-4;
}

.password-toggle {
  @apply h-9 w-9 min-w-9;

  &__icon {
    @apply h-5.5 w-5.5;
  }

  &-tooltip__content {
    @apply border border-default-200 bg-gradient-to-br from-primary/15 to-secondary/20 px-4 py-2 dark:from-primary/10 dark:to-secondary/10;
  }
}

.submit-spinner {
  &__base {
    @apply absolute h-full w-full bg-primary-500;
  }

  &__wrapper {
    @apply h-auto transform-none pt-3;
  }
}

.captcha {
  @apply relative overflow-hidden rounded-medium;

  &::before {
    @apply pointer-events-none absolute bottom-0 left-0 right-0 top-0 rounded-medium border-medium border-default-200 transition-all content-empty;
  }

  & > div {
    @apply h-19 w-[18.875rem];
  }

  &__wrapper {
    @apply relative flex min-h-10 w-full justify-center;
  }

  &__spinner {
    @apply absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2;
  }

  &.-error {
    &::before {
      @apply border-danger;
    }
  }

  &.-valid {
    &::before {
      @apply border-success;
    }
  }
}
