'use client'

import { useEffect, useState } from 'react'
import {
  addToast,
  Alert,
  Button,
  closeAll,
  Progress,
  Spinner,
  Tooltip
} from '@heroui/react'
import clsx from 'clsx'
import { useLocale, useTranslations } from 'next-intl'
import { useTheme } from 'next-themes'
import ReCAPTCHA from 'react-google-recaptcha'
import { useBoolean } from 'usehooks-ts'
import ControlledForm from '@components/controlled-form'
import ControlledInput from '@components/controlled-form/input'
import useAppForm from '@hooks/app-form'
import useAsyncEffect from '@hooks/async-effect'
import { type LanguageSlug } from '@i18n/settings'
import EyeCloseIcon from '@icons/eye-close.svg'
import EyeIcon from '@icons/eye.svg'
import { type ILoginFormFields } from '@partials/login-form/types'
import { validationRules } from '@partials/login-form/validation-rules'
import { Logo } from '@partials/logo'
import SplashScreen from '@partials/splash-screen'
import {
  captchaConfigGet,
  loginInit,
  resetCaptchaConfig,
  selectAuthState
} from '@reducers/auth-reducer'
import { useAppDispatch, useAppSelector } from '@store/client'
import type { ResolvedTheme } from '@types'
import styles from '@partials/login-form/styles.module'

const LoginForm = () => {
  const tForm = useTranslations('loginPage.form')
  const tCommon = useTranslations('common')
  const { resolvedTheme } = useTheme() as { resolvedTheme: ResolvedTheme }
  const locale = useLocale() as LanguageSlug
  const dispatch = useAppDispatch()
  const reCaptchaHasError = useBoolean(false)
  const [passwordIsVisible, setPasswordIsVisible] = useState(false)
  const [verificationCode, setVerificationCode] = useState<string | null>(null)

  const {
    step: authStep,
    isPending: authPending,
    errors: authErrors,
    captcha
  } = useAppSelector(selectAuthState)

  const methods = useAppForm<ILoginFormFields>({
    defaultValues: {
      username: '',
      password: ''
    },
    validationRules: validationRules({
      t: useTranslations('common.validation')
    })
  })

  const {
    handleSubmit,
    formState: { isSubmitting }
  } = methods

  const onSubmit = handleSubmit(async (data) => {
    await dispatch(
      loginInit({
        ...data,
        ...(verificationCode && { reCaptcha: verificationCode })
      })
    )
  })

  useEffect(() => {
    dispatch(resetCaptchaConfig())
  }, [])

  useAsyncEffect(async () => {
    if (!captcha.isFetched && !captcha.isPending)
      await dispatch(captchaConfigGet())
  })

  useEffect(() => {
    if (!captcha.isPending && captcha.errors.length) {
      closeAll()

      captcha.errors.forEach((error) => {
        addToast({
          color: 'danger',
          title: tCommon('error.captchaConfigCantFetched'),
          description: error,
          endContent: (
            <Button
              color='danger'
              variant='flat'
              onPress={async () => await dispatch(captchaConfigGet())}>
              {tCommon('tryAgainAction')}
            </Button>
          )
        })
      })
    }
  }, [captcha.isPending, captcha.errors])

  useEffect(() => {
    if (!authPending && authErrors.length) {
      closeAll()

      authErrors.forEach((error) => {
        addToast({
          color: 'danger',
          title: tForm('error.loginFailed'),
          description: error
        })
      })
    }
  }, [authPending, authErrors])

  if (captcha.isPending || (!captcha.isFetched && !captcha.errors.length))
    return authStep === 'instant' ? (
      <Progress isIndeterminate size='sm' />
    ) : (
      <SplashScreen />
    )

  return (
    <div className={styles.login}>
      <div className={styles.header}>
        <div className={styles['header-main']}>
          <Logo className={styles['header-main__logo']} />
          <span className={styles['header-main__title']}>
            {tForm('header.mainTitle')}
          </span>
        </div>
        <div className={styles.header__title}> {tForm('header.title')}</div>
      </div>
      <ControlledForm
        className={styles.form}
        methods={methods}
        onSubmit={onSubmit}>
        <ControlledInput
          name='username'
          type='text'
          label={tForm('usernameLabel')}
          variant='bordered'
        />
        <ControlledInput
          name='password'
          type={passwordIsVisible ? 'text' : 'password'}
          label={tForm('passwordLabel')}
          variant='bordered'
          endContent={
            <Tooltip
              placement='right'
              content={
                passwordIsVisible
                  ? tForm('passwordHideTooltip')
                  : tForm('passwordShowTooltip')
              }
              classNames={{
                content: styles['password-toggle-tooltip__content']
              }}>
              <Button
                isIconOnly
                className={styles['password-toggle']}
                radius='full'
                variant='light'
                size='lg'
                onPress={() => setPasswordIsVisible(!passwordIsVisible)}>
                {passwordIsVisible ? (
                  <EyeCloseIcon className={styles['password-toggle__icon']} />
                ) : (
                  <EyeIcon className={styles['password-toggle__icon']} />
                )}
              </Button>
            </Tooltip>
          }
        />
        {captcha.captchaEnabled && (
          <div className={styles.captcha__wrapper}>
            <Spinner size='lg' classNames={{ base: styles.captcha__spinner }} />
            <ReCAPTCHA
              className={clsx(
                styles.captcha,
                reCaptchaHasError.value
                  ? styles['-error']
                  : verificationCode && styles['-valid']
              )}
              hl={locale}
              theme={resolvedTheme}
              sitekey={captcha.siteKey}
              onChange={(code) => {
                setVerificationCode(code)
                reCaptchaHasError.setFalse()
              }}
              onExpired={reCaptchaHasError.setTrue}
              onErrored={reCaptchaHasError.setTrue}
            />
          </div>
        )}
        {authErrors.map((error) => (
          <Alert key={error} variant='faded' color='danger' title={error} />
        ))}
        <Button
          fullWidth
          isLoading={isSubmitting || authPending}
          isDisabled={
            !captcha.isFetched || (captcha.captchaEnabled && !verificationCode)
          }
          spinner={
            <Spinner
              size='lg'
              variant='wave'
              color='white'
              classNames={{
                base: styles['submit-spinner__base'],
                wrapper: styles['submit-spinner__wrapper']
              }}
            />
          }
          type='submit'
          size='lg'
          color='primary'>
          {tForm('submit')}
        </Button>
      </ControlledForm>
    </div>
  )
}

export default LoginForm
