'use client'

import { useEffect, useState } from 'react'
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger
} from '@heroui/react'
import { useTranslations } from 'next-intl'
import { useTheme } from 'next-themes'
import DarkModeIcon from '@icons/dark-mode.svg'
import LightModeIcon from '@icons/light-mode.svg'
import SystemModeIcon from '@icons/system-color-mode.svg'
import type { ResolvedTheme, Theme, Themes } from '@types'
import styles from '@partials/color-mode-switcher/styles.module'

const ColorModeSwitcher = () => {
  const t = useTranslations('common.colorMode')
  const themeProps = useTheme()
  const [mounted, setMounted] = useState(false)

  const { theme, resolvedTheme, themes } = themeProps as {
    theme: Theme
    resolvedTheme: ResolvedTheme
    themes: Themes
  }

  const themeIcon = (theme: Theme, className: string) =>
    ({
      light: <LightModeIcon className={className} />,
      dark: <DarkModeIcon className={className} />,
      system: <SystemModeIcon className={className} />
    })[theme]

  useEffect(() => setMounted(true))

  return mounted ? (
    <Dropdown
      offset={2}
      placement='bottom-end'
      classNames={{ content: styles.dropdown__content }}>
      <DropdownTrigger>
        <Button isIconOnly radius='full' variant='light' size='lg'>
          {themeIcon(resolvedTheme, styles.trigger__icon)}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disabledKeys={[theme]}
        variant='flat'
        itemClasses={{
          base: styles.item,
          title: styles.item__content
        }}>
        {themes.map((value) => (
          <DropdownItem
            key={value}
            textValue={value}
            startContent={themeIcon(value, styles.item__icon)}
            onPress={() => themeProps.setTheme(value)}>
            {t(value)}
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  ) : (
    <Button
      isLoading
      isDisabled
      isIconOnly
      radius='full'
      variant='light'
      size='lg'
    />
  )
}

export default ColorModeSwitcher
