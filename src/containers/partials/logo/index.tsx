import clsx from 'clsx'
import MainLogoColored from '@images/main-logo-colored.svg'
import MainLogoWhite from '@images/main-logo-white.svg'
import type { ILogoProps } from '@partials/logo/types'
import styles from '@partials/logo/styles.module'

export const Logo = ({ className }: ILogoProps) => (
  <div className={className}>
    <MainLogoWhite className={clsx(styles.logo, styles['logo-white'])} />
    <MainLogoColored className={clsx(styles.logo, styles['logo-colored'])} />
  </div>
)
