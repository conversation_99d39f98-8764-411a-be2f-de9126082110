import type { IApiUrl } from '@api/types'

const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL

export const apiUrl: IApiUrl = ({ path, segments = [], queries, args }) =>
  `${apiBaseUrl?.endsWith('/') ? apiBaseUrl : `${apiBaseUrl}/`}${path}${
    segments.length ? `/${segments.join('/')}` : ''
  }${
    queries && Object.keys(queries).length
      ? `?${Object.keys(queries)
          .map((qKey) => `${qKey}=${queries[qKey]}`)
          .join('&')}`
      : ''
  }`.replace(/{(\w+)}/g, (sKey: string, aKey: string) => args?.[aKey] ?? sKey)

export const endpoints = {
  auth: {
    captchaConfig: 'get-captcha-config',
    login: 'login'
  },
  user: {
    me: 'currentUser'
  }
}
