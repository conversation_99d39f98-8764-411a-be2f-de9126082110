import axiosBase from 'axios'
import { getLanguage } from '@i18n/services'

const axios = axiosBase.create({
  withCredentials: true,
  withXSRFToken: true,
  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-XSRF-TOKEN'
})

axios.interceptors.request.use(async (requestConfig) => {
  requestConfig.headers['X-APP-LANGUAGE'] = await getLanguage()
  requestConfig.headers['X-REQUESTED-WITH'] = 'XMLHttpRequest'

  return requestConfig
}, null)

export default axios
