import Link from 'next/link'
import { But<PERSON> } from '@heroui/react'
import { useTranslations } from 'next-intl'
import ColorModeSwitcher from '@partials/color-mode-switcher'
import LanguageSwitcher from '@partials/language-switcher'
import { routes } from '@settings'

const DetailView = () => {
  const t = useTranslations('detailPage')

  return (
    <div className='mx-auto h-[2000px] max-w-6xl p-4'>
      <div className='mb-4 text-3xl font-extrabold'>{t('title')}</div>
      <div className='mb-4'>
        <ColorModeSwitcher />
      </div>
      <div className='mb-4'>
        <LanguageSwitcher />
      </div>
      <Button as={Link} href={routes.guest.login} className='mb-4'>
        Go to Login Page
      </Button>
      <Button as={Link} href={routes.secure.homepage} className='mb-4'>
        Go to Home Page
      </Button>
    </div>
  )
}

export default DetailView
