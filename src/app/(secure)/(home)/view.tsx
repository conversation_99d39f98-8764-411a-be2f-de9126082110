import Link from 'next/link'
import { Button, Input } from '@heroui/react'
import { useTranslations } from 'next-intl'
import ColorModeSwitcher from '@partials/color-mode-switcher'
import LanguageSwitcher from '@partials/language-switcher'
import { routes } from '@settings'

const HomeView = () => {
  const t = useTranslations('homepage')

  return (
    <div className='mx-auto max-w-6xl p-4'>
      <div className='mb-4 text-3xl font-extrabold'>{t('title')}</div>
      <div className='mb-4 flex flex-col gap-4'>
        <div className='flex flex-wrap items-center justify-between gap-4'>
          <Button color='primary' variant='solid'>
            Primary Solid
          </Button>
          <Button color='primary' variant='faded'>
            Primary Faded
          </Button>
          <Button color='primary' variant='bordered'>
            Primary Bordered
          </Button>
          <Button color='primary' variant='light'>
            Primary Light
          </Button>
          <Button color='primary' variant='flat'>
            Primary Flat
          </Button>
          <Button color='primary' variant='ghost'>
            Primary Ghost
          </Button>
          <Button color='primary' variant='shadow'>
            Primary Shadow
          </Button>
        </div>
        <div className='flex flex-wrap items-center justify-between gap-4'>
          <Button variant='solid'>Default Solid</Button>
          <Button variant='faded'>Default Faded</Button>
          <Button variant='bordered'>Default Bordered</Button>
          <Button variant='light'>Default Light</Button>
          <Button variant='flat'>Default Flat</Button>
          <Button variant='ghost'>Default Ghost</Button>
          <Button variant='shadow'>Default Shadow</Button>
        </div>
        <Input type='text' label='HERO INPUT' />
      </div>
      <div className='mb-4'>
        <ColorModeSwitcher />
      </div>
      <div className='mb-4'>
        <LanguageSwitcher />
      </div>
      <Button as={Link} href={routes.guest.login} className='mb-4'>
        Go to Login Page
      </Button>
      <Button as={Link} href={routes.secure.detail} className='mb-4'>
        Go to Detail Page
      </Button>
    </div>
  )
}

export default HomeView
