'use server'

import { type Metadata } from 'next'
import { getLocale, getMessages, getTranslations } from 'next-intl/server'
import favicon from '@icons/favicon.ico'
import AppProviders from '@providers/app-providers'
import '@styles/main'

export const generateMetadata = async (): Promise<Metadata> => {
  const t = await getTranslations('app.meta')

  return {
    title: {
      template: t('title.template'),
      default: t('title.default')
    },
    icons: [
      {
        rel: 'icon',
        url: favicon.src
      }
    ]
  }
}

const Layout = async ({
  children
}: Readonly<{ children: React.ReactNode }>) => {
  const locale = await getLocale()
  const messages = await getMessages({ locale })

  return (
    <html suppressHydrationWarning lang={locale}>
      <body>
        <AppProviders locale={locale} messages={messages}>
          {children}
        </AppProviders>
      </body>
    </html>
  )
}

export default Layout
