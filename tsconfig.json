{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      // Assets
      "@videos/*": ["./src/assets/videos/*"],
      "@images/*": ["./src/assets/images/*"],
      "@icons/*": ["./src/assets/icons/*"],
      "@styles/*": ["./src/assets/styles/*"],
      "@assets/*": ["./src/assets/*"],
      // Store & Api
      "@api/*": ["./src/api/*"],
      "@reducers/*": ["./src/store/reducers/*"],
      "@storeUtils/*": ["./src/store/utils/*"],
      "@responseTypes/*": ["./src/store/types/response/*"],
      "@storeTypes/*": ["./src/store/types/*"],
      "@storage/*": ["./src/store/storage/*"],
      "@store": ["./src/store/index"],
      "@store/*": ["./src/store/*"],
      // View Essentials
      "@settings": ["./src/settings"],
      "@views/*": ["./src/containers/views/*"],
      "@partials/*": ["./src/containers/partials/*"],
      "@layouts/*": ["./src/containers/layouts/*"],
      "@providers/*": ["./src/containers/providers/*"],
      "@containers/*": ["./src/containers/*"],
      "@components/*": ["./src/components/*"],
      "@i18n/*": ["./src/i18n/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@utils/*": ["./src/utils/*"],
      "@types": ["./src/types/index"],
      "@types/*": ["./src/types/*"],
      // Main
      "@app/*": ["./src/app/*"],
      "@/*": ["./src/*"]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.mts",
    "**/*.cts",
    "**/*.d.ts",
    "next-env.d.ts",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
